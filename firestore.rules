rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null; // Allow reading other users for chat
    }
    
    // Chat rooms collection - participants can read/write
    match /chatRooms/{chatRoomId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      allow create: if request.auth != null && 
        request.auth.uid in request.resource.data.participants;
    }
    
    // Messages collection - participants can read/write
    match /messages/{messageId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.senderId;
    }
    
    // Notifications collection - system can write, users can read their own
    match /notifications/{notificationId} {
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow write: if request.auth != null;
    }
    
    // FCM tokens collection - users can write their own tokens
    match /fcm_tokens/{tokenId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Calls collection - caller and receiver can read/write call data
    match /calls/{callId} {
      allow read, write: if request.auth != null &&
        (request.auth.uid == resource.data.callerId ||
         request.auth.uid == resource.data.receiverId);
      allow create: if request.auth != null &&
        (request.auth.uid == request.resource.data.callerId ||
         request.auth.uid == request.resource.data.receiverId);
    }
    
    // Test collection for cloud function testing
    match /test/{testId} {
      allow read, write: if request.auth != null;
    }
    
    // Cleanup collection for maintenance
    match /cleanup/{docId} {
      allow read, write: if request.auth != null;
    }
  }
}
