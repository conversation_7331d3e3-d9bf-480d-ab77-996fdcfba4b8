import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/models/contact_model.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/providers/contact_provider.dart';
import 'package:tolk/services/chat_service.dart';
import 'package:tolk/utils/app_colors.dart';

import 'package:tolk/widgets/attachment_bottom_sheet.dart';
import 'package:tolk/widgets/chat/translated_message_widget.dart';
import 'package:tolk/widgets/media_message_widget.dart';
import 'package:tolk/widgets/voice_recorder_widget.dart';
import 'package:tolk/widgets/emoji_picker_widget.dart';
import 'package:tolk/services/call_service.dart'; // Keep if other parts use it, remove if only for these calls
import 'package:tolk/services/call_handler_service.dart'; // Import the new service
import 'package:tolk/widgets/chat/call_message_widget.dart';
import 'package:tolk/screens/chat/group_details_screen.dart'; // Import the new screen

class ChatScreen extends StatefulWidget {
  final String chatRoomId;
  final UserModel? otherUser;

  const ChatScreen({super.key, required this.chatRoomId, this.otherUser});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen>
    with AutomaticKeepAliveClientMixin {
  final ChatService _chatService = ChatService();
  // final CallService _callService = CallService(); // Remove old CallService instance
  final CallHandlerService _callHandlerService =
      CallHandlerService(); // Add new service instance
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  bool _isRecording = false;
  bool _showEmojiPicker = false;
  final FocusNode _textFieldFocusNode = FocusNode();

  // Pagination variables
  List<Message> _allMessages = [];
  bool _isLoadingMore = false;
  bool _hasMoreMessages = true;
  static const int _messagesPerPage = 15;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Mark messages as read after screen is fully built for smooth navigation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _markMessagesAsRead();
    });

    // Listen to text changes to update send/mic button
    _messageController.addListener(() {
      setState(() {});
    });

    // Listen to focus changes to hide emoji picker
    _textFieldFocusNode.addListener(() {
      if (_textFieldFocusNode.hasFocus && _showEmojiPicker) {
        setState(() {
          _showEmojiPicker = false;
        });
      }
    });

    // Add scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // User is near the top, load more messages
      _loadMoreMessages();
    }
  }

  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore || !_hasMoreMessages || _allMessages.isEmpty) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final lastMessage = _allMessages.last;
      final moreMessages = await _chatService.loadMoreMessages(
        widget.chatRoomId,
        lastMessage.timestamp,
        limit: _messagesPerPage,
      );

      if (moreMessages.isNotEmpty) {
        setState(() {
          _allMessages.addAll(moreMessages);
          _hasMoreMessages = moreMessages.length == _messagesPerPage;
        });
      } else {
        setState(() {
          _hasMoreMessages = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading more messages: $e');
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _textFieldFocusNode.dispose();
    super.dispose();
  }

  Future<void> _markMessagesAsRead() async {
    await _chatService.markMessagesAsRead(widget.chatRoomId);
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    // Clear text field immediately for smooth UX
    _messageController.clear();

    try {
      await _chatService.sendMessage(chatRoomId: widget.chatRoomId, text: text);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error sending message: $e')));

        // Restore text if sending failed
        _messageController.text = text;
      }
    }
  }

  Future<void> _handleImageUpload(XFile imageFile) async {
    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    try {
      await _chatService.sendImageMessage(
        chatRoomId: widget.chatRoomId,
        imageFile: imageFile,
        onProgress: (progress) {
          setState(() {
            _uploadProgress = progress;
          });
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error uploading image: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
          _uploadProgress = 0.0;
        });
      }
    }
  }

  Future<void> _handleFileUpload(PlatformFile file) async {
    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    try {
      await _chatService.sendFileMessage(
        chatRoomId: widget.chatRoomId,
        file: file,
        onProgress: (progress) {
          setState(() {
            _uploadProgress = progress;
          });
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error uploading file: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
          _uploadProgress = 0.0;
        });
      }
    }
  }

  Future<void> _handleVideoUpload(XFile videoFile) async {
    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    try {
      await _chatService.sendVideoMessage(
        chatRoomId: widget.chatRoomId,
        videoFile: videoFile,
        onProgress: (progress) {
          setState(() {
            _uploadProgress = progress;
          });
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error uploading video: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
          _uploadProgress = 0.0;
        });
      }
    }
  }

  Future<void> _handleLocationShare(Map<String, dynamic> locationData) async {
    print(
      '📱 [CHAT] Received location data from attachment sheet: $locationData',
    );
    try {
      print('📱 [CHAT] Calling chat service to send location message...');
      await _chatService.sendLocationMessage(
        chatRoomId: widget.chatRoomId,
        locationData: locationData,
      );
      print('📱 [CHAT] Location message sent successfully!');
    } catch (e) {
      print('📱 [CHAT] Error sending location message: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error sharing location: $e')));
      }
    }
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => AttachmentBottomSheet(
            onImageSelected: _handleImageUpload,
            onFileSelected: _handleFileUpload,
            onVideoSelected: _handleVideoUpload,
            onLocationSelected: _handleLocationShare,
          ),
    );
  }

  Future<void> _handleVoiceRecording(String audioPath) async {
    setState(() {
      _isRecording = false;
    });

    try {
      await _chatService.sendVoiceMessage(
        chatRoomId: widget.chatRoomId,
        audioPath: audioPath,
        onProgress: (progress) {
          setState(() {
            _uploadProgress = progress;
          });
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending voice message: $e')),
        );
      }
    }
  }

  void _startVoiceRecording() {
    setState(() {
      _isRecording = true;
      _showEmojiPicker = false;
    });
    _textFieldFocusNode.unfocus();
  }

  void _cancelVoiceRecording() {
    setState(() {
      _isRecording = false;
    });
  }

  void _toggleEmojiPicker() {
    setState(() {
      _showEmojiPicker = !_showEmojiPicker;
    });

    if (_showEmojiPicker) {
      _textFieldFocusNode.unfocus();
    } else {
      _textFieldFocusNode.requestFocus();
    }
  }

  void _onEmojiSelected(String emoji) {
    final text = _messageController.text;
    final selection = _messageController.selection;
    final newText = text.replaceRange(selection.start, selection.end, emoji);
    _messageController.text = newText;
    _messageController.selection = TextSelection.fromPosition(
      TextPosition(offset: selection.start + emoji.length),
    );
  }

  void _onBackspacePressed() {
    final text = _messageController.text;
    final selection = _messageController.selection;
    if (selection.start > 0) {
      final newText = text.replaceRange(selection.start - 1, selection.end, '');
      _messageController.text = newText;
      _messageController.selection = TextSelection.fromPosition(
        TextPosition(offset: selection.start - 1),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final currentUser = Provider.of<UserProvider>(context).currentUser;
    if (currentUser == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: FutureBuilder<ChatRoom?>(
          future: _chatService.getChatRoomById(
            widget.chatRoomId,
          ), // Fetch chat room
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey,
                    child: const Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Loading...',
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            }

            if (snapshot.hasError ||
                !snapshot.hasData ||
                snapshot.data == null) {
              return Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey,
                    child: const Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Error loading chat info',
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            }

            final chatRoom = snapshot.data!;
            String displayName;
            String? profilePicture;
            bool isOnline = false;
            DateTime? lastSeen;

            if (chatRoom.isGroupChat) {
              displayName = chatRoom.groupName ?? 'Group Chat';
              profilePicture = chatRoom.groupImage;
              // For group chats, online status and last seen are not typically shown in the header
            } else {
              // Logic for individual chat
              final contactProvider = Provider.of<ContactProvider>(
                context,
                listen: false,
              );
              displayName =
                  widget.otherUser?.name ??
                  widget.otherUser?.phoneNumber ??
                  'Unknown';

              if (widget.otherUser != null) {
                final savedContact = contactProvider.savedContacts.firstWhere(
                  (contact) => contact.phoneNumbers.contains(
                    widget.otherUser!.phoneNumber,
                  ),
                  orElse:
                      () => ContactModel(
                        id: '',
                        displayName: '',
                        phoneNumbers: [],
                      ),
                );

                // Use saved contact name if available
                if (savedContact.displayName.isNotEmpty) {
                  displayName = savedContact.displayName;
                }
                profilePicture = widget.otherUser?.profilePicture;
                isOnline = widget.otherUser?.isOnline ?? false;
                lastSeen = widget.otherUser?.lastSeen;
              }
            }

            return InkWell(
              onTap:
                  chatRoom.isGroupChat
                      ? () {
                        // Navigate to group details screen
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => GroupDetailsScreen(
                                  chatRoomId: widget.chatRoomId,
                                ),
                          ),
                        );
                      }
                      : null, // Disable tap for individual chats
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey,
                    backgroundImage:
                        profilePicture != null
                            ? CachedNetworkImageProvider(profilePicture)
                            : null,
                    child:
                        profilePicture == null
                            ? Icon(
                              chatRoom.isGroupChat ? Icons.group : Icons.person,
                              color: Colors.white,
                              size: 16,
                            )
                            : null,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          displayName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (!chatRoom.isGroupChat &&
                            widget.otherUser !=
                                null) // Only show status for individual chats
                          Text(
                            isOnline
                                ? 'Online'
                                : lastSeen != null
                                ? 'Last seen ${_formatLastSeen(lastSeen)}'
                                : 'Offline',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        backgroundColor: Colors.grey[900],
        actions: [
          // Audio call button (only for individual chats)
          if (widget.otherUser != null)
            IconButton(
              icon: const Icon(Icons.call, color: Colors.white),
              onPressed: _makeAudioCall,
            ),
          // Video call button (only for individual chats)
          if (widget.otherUser != null)
            IconButton(
              icon: const Icon(Icons.videocam, color: Colors.white),
              onPressed: _makeVideoCall,
            ),
          // More options button
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: () {
              // Capture context before any async/await
              final parentContext = context;
              showModalBottomSheet(
                context: parentContext,
                backgroundColor: AppColors.splashColor,
                builder:
                    (sheetContext) => FutureBuilder<ChatRoom?>(
                      future: _chatService.getChatRoomById(widget.chatRoomId),
                      builder: (context, snapshot) {
                        final chatRoom = snapshot.data;
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (chatRoom != null && chatRoom.isGroupChat)
                              ListTile(
                                leading: const Icon(
                                  Icons.edit,
                                  color: Colors.white,
                                ),
                                title: const Text(
                                  'Edit Group Name',
                                  style: TextStyle(color: Colors.white),
                                ),
                                onTap: () {
                                  Navigator.pop(sheetContext);
                                  final controller = TextEditingController(
                                    text: chatRoom.groupName ?? '',
                                  );
                                  showDialog<String>(
                                    context: parentContext,
                                    builder:
                                        (dialogContext) => AlertDialog(
                                          backgroundColor:
                                              AppColors.dialogColor,
                                          title: const Text('Edit Group Name'),
                                          content: TextField(
                                            controller: controller,
                                            decoration: const InputDecoration(
                                              hintText: 'Enter new group name',
                                            ),
                                          ),
                                          actions: [
                                            TextButton(
                                              onPressed:
                                                  () => Navigator.pop(
                                                    dialogContext,
                                                  ),
                                              child: const Text('Cancel'),
                                            ),
                                            ElevatedButton(
                                              onPressed:
                                                  () => Navigator.pop(
                                                    dialogContext,
                                                    controller.text.trim(),
                                                  ),
                                              child: const Text('Save'),
                                            ),
                                          ],
                                        ),
                                  ).then((result) async {
                                    if (result != null &&
                                        result.isNotEmpty &&
                                        result != chatRoom.groupName) {
                                      try {
                                        await _chatService.updateGroupName(
                                          widget.chatRoomId,
                                          result,
                                        );
                                        if (mounted) {
                                          ScaffoldMessenger.of(
                                            parentContext,
                                          ).showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                'Group name updated',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                ),
                                              ),
                                              backgroundColor:
                                                  AppColors.appColor,
                                            ),
                                          );
                                          setState(() {});
                                        }
                                      } catch (e) {
                                        if (mounted) {
                                          ScaffoldMessenger.of(
                                            parentContext,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Failed to update group name: $e',
                                              ),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                        }
                                      }
                                    }
                                  });
                                },
                              ),
                            if (chatRoom != null && chatRoom.isGroupChat)
                              ListTile(
                                leading: const Icon(
                                  Icons.photo_camera,
                                  color: Colors.white,
                                ),
                                title: const Text(
                                  'Change Group Picture',
                                  style: TextStyle(color: Colors.white),
                                ),
                                onTap: () async {
                                  Navigator.pop(sheetContext);
                                  final ImagePicker picker = ImagePicker();
                                  final XFile? image = await picker.pickImage(
                                    source: ImageSource.gallery,
                                  );
                                  if (image == null) return;
                                  try {
                                    final downloadUrl = await _chatService
                                        .uploadGroupImage(
                                          widget.chatRoomId,
                                          image,
                                        );
                                    await _chatService.updateGroupImage(
                                      widget.chatRoomId,
                                      downloadUrl,
                                    );
                                    if (mounted) {
                                      ScaffoldMessenger.of(
                                        parentContext,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Group picture updated',
                                            style: TextStyle(
                                              color: Colors.white,
                                            ),
                                          ),
                                          backgroundColor: AppColors.appColor,
                                        ),
                                      );
                                      setState(() {});
                                    }
                                  } catch (e) {
                                    if (mounted) {
                                      ScaffoldMessenger.of(
                                        parentContext,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'Failed to update group picture: $e',
                                          ),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                    }
                                  }
                                },
                              ),
                            ListTile(
                              leading: const Icon(
                                Icons.delete,
                                color: Colors.red,
                              ),
                              title: const Text(
                                'Delete conversation',
                                style: TextStyle(color: Colors.white),
                              ),
                              onTap: () {
                                Navigator.pop(sheetContext);
                                _showDeleteConfirmation();
                              },
                            ),
                          ],
                        );
                      },
                    ),
              );
            },
          ),
        ],
      ),
      backgroundColor: AppColors.splashColor,
      body: Column(
        children: [
          Expanded(
            child: FutureBuilder<ChatRoom?>(
              // Fetch ChatRoom first
              future: _chatService.getChatRoomById(widget.chatRoomId),
              builder: (context, chatRoomSnapshot) {
                if (chatRoomSnapshot.connectionState ==
                    ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(color: AppColors.appColor),
                  );
                }
                if (chatRoomSnapshot.hasError ||
                    !chatRoomSnapshot.hasData ||
                    chatRoomSnapshot.data == null) {
                  return Center(
                    child: Text(
                      'Error loading chat room info: ${chatRoomSnapshot.error}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  );
                }
                final chatRoom = chatRoomSnapshot.data!;
                final isGroupChat = chatRoom.isGroupChat;

                return StreamBuilder<List<Message>>(
                  stream: _chatService.getMessagesPaginated(
                    widget.chatRoomId,
                    limit: _messagesPerPage,
                  ),
                  builder: (context, snapshot) {
                    // Show loading only on initial load, not on subsequent updates
                    if (snapshot.connectionState == ConnectionState.waiting &&
                        !snapshot.hasData) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.appColor,
                        ),
                      );
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text(
                          'Error: ${snapshot.error}',
                          style: const TextStyle(color: Colors.white),
                        ),
                      );
                    }

                    final latestMessages = snapshot.data ?? [];

                    // Merge latest messages with existing paginated messages
                    _mergeMessages(latestMessages);

                    if (_allMessages.isEmpty) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.chat_bubble_outline,
                              size: 80,
                              color: Colors.white54,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No messages yet',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Start the conversation',
                              style: TextStyle(color: Colors.white70),
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      reverse: true,
                      itemCount: _allMessages.length + (_isLoadingMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        // Show loading indicator at the top (end of list due to reverse)
                        if (index == _allMessages.length) {
                          return const Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Center(
                              child: CircularProgressIndicator(
                                color: AppColors.appColor,
                              ),
                            ),
                          );
                        }

                        final message = _allMessages[index];
                        final isMe = message.senderId == currentUser.uid;
                        final showTime =
                            index == 0 ||
                            _allMessages[index - 1].senderId !=
                                message.senderId ||
                            _shouldShowTimestamp(
                              _allMessages[index].timestamp,
                              _allMessages[index - 1].timestamp,
                            );

                        return _buildMessageBubble(
                          message,
                          isMe,
                          showTime,
                          isGroupChat: isGroupChat, // Pass isGroupChat
                          key: ValueKey(message.id),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
          _buildMessageInput(),
        ],
      ),
    );
  }

  void _mergeMessages(List<Message> latestMessages) {
    if (latestMessages.isEmpty) return;

    // Create a map of existing messages for quick lookup by ID
    final Map<String, Message> existingMessagesMap = {
      for (var msg in _allMessages) msg.id: msg,
    };

    // Process latest messages: update existing or add new
    for (final latestMsg in latestMessages) {
      if (existingMessagesMap.containsKey(latestMsg.id)) {
        // If message with this ID exists, update it in the map
        existingMessagesMap[latestMsg.id] = latestMsg;
      } else {
        // If it's a new message, add it to the map
        existingMessagesMap[latestMsg.id] = latestMsg;
      }
    }

    // Convert map back to a list and sort by timestamp (newest first for reverse ListView)
    _allMessages = existingMessagesMap.values.toList();
    _allMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Widget _buildMessageBubble(
    Message message,
    bool isMe,
    bool showTime, {
    required bool isGroupChat,
    Key? key,
  }) {
    if (message.type == MessageType.audioCall ||
        message.type == MessageType.videoCall) {
      return CallMessageWidget(
        message: message,
        isMe: isMe,
        chatPartnerUser: widget.otherUser,
        onInitiateCallBack: (MessageType callType) {
          final currentUser =
              Provider.of<UserProvider>(context, listen: false).currentUser;
          if (currentUser == null || widget.otherUser == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Unable to start call back.')),
            );
            return;
          }
          if (callType == MessageType.audioCall) {
            _callHandlerService.makeAudioCall(
              context: context,
              caller: currentUser,
              receiver: widget.otherUser,
            );
          } else if (callType == MessageType.videoCall) {
            _callHandlerService.makeVideoCall(
              context: context,
              caller: currentUser,
              receiver: widget.otherUser,
            );
          }
        },
      );
    }

    // Wrap the message bubble with GestureDetector for long-press delete
    return GestureDetector(
      key: key,
      onLongPress:
          isMe
              ? () async {
                final confirm = await showDialog<bool>(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        backgroundColor: AppColors.dialogColor,
                        title: const Text('Delete message?'),
                        content: const Text(
                          'This message will be deleted for you and cannot be undone.',
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context, false),
                            child: const Text('Cancel'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.pop(context, true),
                            child: const Text(
                              'Delete',
                              style: TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                );
                if (confirm == true) {
                  try {
                    await _chatService.deleteMessage(message.id);
                    if (mounted) {
                      setState(() {
                        _allMessages.removeWhere((m) => m.id == message.id);
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Message deleted')),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Failed to delete message: $e')),
                      );
                    }
                  }
                }
              }
              : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Column(
          crossAxisAlignment:
              isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            // Display sender info for group chats (if not current user)
            if (isGroupChat && !isMe)
              FutureBuilder<UserModel?>(
                future: Provider.of<UserProvider>(
                  context,
                  listen: false,
                ).getUserById(message.senderId),
                builder: (context, senderSnapshot) {
                  if (senderSnapshot.connectionState ==
                          ConnectionState.waiting ||
                      !senderSnapshot.hasData ||
                      senderSnapshot.data == null) {
                    return const SizedBox.shrink();
                  }
                  final sender = senderSnapshot.data!;
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 12,
                          backgroundColor: Colors.grey,
                          backgroundImage:
                              sender.profilePicture != null
                                  ? CachedNetworkImageProvider(
                                    sender.profilePicture!,
                                  )
                                  : null,
                          child:
                              sender.profilePicture == null
                                  ? const Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 12,
                                  )
                                  : null,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          sender.name ?? sender.phoneNumber ?? 'Unknown',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            if (showTime)
              Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  _formatMessageTime(message.timestamp),
                  style: const TextStyle(fontSize: 12, color: Colors.white70),
                ),
              ),
            // For voice messages, don't add extra container - let MediaMessageWidget handle styling
            message.type == MessageType.audio
                ? Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    MediaMessageWidget(message: message, isMe: isMe),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          DateFormat.jm().format(message.timestamp),
                          style: TextStyle(
                            fontSize: 10,
                            color: isMe ? Colors.white70 : Colors.white54,
                          ),
                        ),
                        if (isMe) ...[
                          const SizedBox(width: 4),
                          Icon(
                            _getStatusIcon(message.status),
                            size: 12,
                            color:
                                message.status == MessageStatus.read
                                    ? Colors.blue
                                    : Colors.white70,
                          ),
                        ],
                      ],
                    ),
                  ],
                )
                : Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isMe && message.type == MessageType.text
                            ? AppColors.appColor
                            : message.type == MessageType.text
                            ? Colors.grey[800]
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Use MediaMessageWidget for non-text messages
                      if (message.type != MessageType.text)
                        MediaMessageWidget(message: message, isMe: isMe)
                      else
                        TranslatedMessageWidget(message: message, isMe: isMe),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            DateFormat.jm().format(message.timestamp),
                            style: TextStyle(
                              fontSize: 10,
                              color: isMe ? Colors.white70 : Colors.white54,
                            ),
                          ),
                          if (isMe) ...[
                            const SizedBox(width: 4),
                            Icon(
                              _getStatusIcon(message.status),
                              size: 20,
                              color:
                                  message.status == MessageStatus.read
                                      ? Colors.blue
                                      : Colors.white70,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Column(
      children: [
        // Upload progress indicator
        if (_isUploading)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: Colors.grey[850],
            child: Row(
              children: [
                const Icon(Icons.cloud_upload, color: Colors.white70, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Uploading... ${(_uploadProgress * 100).toInt()}%',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: _uploadProgress,
                        backgroundColor: Colors.grey[700],
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.appColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        // Voice recorder overlay
        if (_isRecording)
          VoiceRecorderWidget(
            onRecordingComplete: _handleVoiceRecording,
            onCancel: _cancelVoiceRecording,
          )
        else
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            color: Colors.grey[900],
            child: Row(
              children: [
                // File attachment button
                IconButton(
                  icon: const Icon(Icons.attach_file, color: Colors.white70),
                  onPressed: _isUploading ? null : _showAttachmentOptions,
                ),
                // Emoji button
                EmojiButton(
                  isEmojiPickerVisible: _showEmojiPicker,
                  onPressed: _toggleEmojiPicker,
                ),
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    focusNode: _textFieldFocusNode,
                    decoration: const InputDecoration(
                      hintText: 'Type a message',
                      hintStyle: TextStyle(color: Colors.white70),
                      border: InputBorder.none,
                    ),
                    style: const TextStyle(color: Colors.white),
                    maxLines: null,
                    textCapitalization: TextCapitalization.sentences,
                    onTap: () {
                      if (_showEmojiPicker) {
                        setState(() {
                          _showEmojiPicker = false;
                        });
                      }
                    },
                  ),
                ),
                // Voice/Send button
                IconButton(
                  icon:
                      _isUploading
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppColors.appColor,
                            ),
                          )
                          : _messageController.text.trim().isEmpty
                          ? const Icon(Icons.mic, color: AppColors.appColor)
                          : const Icon(Icons.send, color: AppColors.appColor),
                  onPressed:
                      _isUploading
                          ? null
                          : _messageController.text.trim().isEmpty
                          ? _startVoiceRecording
                          : _sendMessage,
                ),
              ],
            ),
          ),
        // Emoji picker
        if (_showEmojiPicker)
          EmojiPickerWidget(
            onEmojiSelected: _onEmojiSelected,
            onBackspacePressed: _onBackspacePressed,
          ),
      ],
    );
  }

  IconData _getStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return Icons.access_time;
      case MessageStatus.sent:
        return Icons.check;
      case MessageStatus.delivered:
        return Icons.done_all;
      case MessageStatus.read:
        return Icons.done_all;
      case MessageStatus.failed:
        return Icons.error_outline;
    }
  }

  String _formatMessageTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      return 'Today';
    } else if (messageDate == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat.yMMMd().format(dateTime); // Jan 5, 2023
    }
  }

  String _formatLastSeen(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else {
      return DateFormat.MMMd().format(dateTime); // Jan 5
    }
  }

  bool _shouldShowTimestamp(DateTime current, DateTime previous) {
    return current.difference(previous).inMinutes > 5;
  }

  void _showComingSoonMessage(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '$feature - Coming Soon!',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.appColor,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Future<void> _showDeleteConfirmation() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.dialogColor,
            title: const Text('Delete conversation?'),
            content: const Text(
              'This will delete all messages in this conversation. This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (confirm == true && mounted) {
      // Delete the chat room
      await _chatService.deleteChatRoom(widget.chatRoomId);

      // Navigate back if still mounted
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  Future<void> _makeAudioCall() async {
    final currentUser =
        Provider.of<UserProvider>(context, listen: false).currentUser;
    // widget.otherUser is already nullable, no need for an explicit null check if the service handles it
    // However, the service expects non-null caller and receiver for the actual call initiation part.
    // The initial check for null currentUser or otherUser is good practice here before calling the service.
    if (currentUser == null || widget.otherUser == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Unable to start call')));
      return;
    }
    await _callHandlerService.makeAudioCall(
      context: context,
      caller: currentUser,
      receiver: widget.otherUser, // Pass otherUser directly
    );
  }

  Future<void> _makeVideoCall() async {
    final currentUser =
        Provider.of<UserProvider>(context, listen: false).currentUser;
    if (currentUser == null || widget.otherUser == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Unable to start call')));
      return;
    }
    await _callHandlerService.makeVideoCall(
      context: context,
      caller: currentUser,
      receiver: widget.otherUser, // Pass otherUser directly
    );
  }
}
