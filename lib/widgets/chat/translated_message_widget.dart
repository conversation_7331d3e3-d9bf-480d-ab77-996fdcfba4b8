import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/services/translation_service.dart';
import 'package:tolk/utils/app_colors.dart';

class TranslatedMessageWidget extends StatefulWidget {
  final Message message;
  final bool isMe;

  const TranslatedMessageWidget({
    super.key,
    required this.message,
    required this.isMe,
  });

  @override
  State<TranslatedMessageWidget> createState() =>
      _TranslatedMessageWidgetState();
}

class _TranslatedMessageWidgetState extends State<TranslatedMessageWidget>
    with AutomaticKeepAliveClientMixin {
  String? _translatedText;
  bool _isTranslating = false;
  bool _showTranslation = false;

  // Static cache to store translations across widget rebuilds
  static final Map<String, Map<String, String>> _translationCache = {};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _checkAndTranslate();
  }

  Future<void> _checkAndTranslate() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final currentUser = userProvider.currentUser;

    if (currentUser == null || widget.message.text == null) return;

    final targetLanguage = currentUser.translationLanguage;
    final messageText = widget.message.text!;

    // Always translate ALL messages to the user's preferred language
    // Skip translation only if target language is English and text appears to be English
    if (targetLanguage == 'en' &&
        TranslationService.isLikelyEnglish(messageText)) {
      return; // Don't translate English text to English
    }

    // Check cache first
    if (_translationCache.containsKey(widget.message.id) &&
        _translationCache[widget.message.id]!.containsKey(targetLanguage)) {
      final cachedTranslation =
          _translationCache[widget.message.id]![targetLanguage]!;
      if (mounted && cachedTranslation != messageText) {
        setState(() {
          _translatedText = cachedTranslation;
          _showTranslation = true;
        });
      }
      return;
    }

    // Auto-translate to user's preferred language
    await _translateMessage(targetLanguage);
  }

  Future<void> _translateMessage(String targetLanguage) async {
    if (_isTranslating || widget.message.text == null) return;

    setState(() {
      _isTranslating = true;
    });

    try {
      final translatedText = await TranslationService.translateText(
        text: widget.message.text!,
        targetLanguage: targetLanguage,
      );

      if (mounted && translatedText != widget.message.text) {
        // Cache the translation
        _translationCache[widget.message.id] ??= {};
        _translationCache[widget.message.id]![targetLanguage] = translatedText;

        setState(() {
          _translatedText = translatedText;
          _showTranslation = true; // Show translation by default
        });
      }
    } catch (e) {
      debugPrint('Translation error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });
      }
    }
  }

  void _toggleTranslation() {
    setState(() {
      _showTranslation = !_showTranslation;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final hasTranslation =
        _translatedText != null && _translatedText != widget.message.text;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Original message text
        Text(
          _showTranslation && hasTranslation
              ? _translatedText!
              : widget.message.text ?? '',
          style: const TextStyle(color: Colors.white),
        ),

        // Translation controls
        if (hasTranslation || _isTranslating) ...[
          const SizedBox(height: 4),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_isTranslating)
                const SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(
                    strokeWidth: 1.5,
                    color: Colors.white70,
                  ),
                )
              else if (hasTranslation)
                GestureDetector(
                  onTap: _toggleTranslation,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.white30, width: 0.5),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.translate, size: 10, color: Colors.white70),
                        const SizedBox(width: 2),
                        Text(
                          _showTranslation ? 'Original' : 'Translated',
                          style: const TextStyle(
                            fontSize: 9,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ],
      ],
    );
  }
}

class TranslationToggleButton extends StatelessWidget {
  final bool showTranslation;
  final VoidCallback onToggle;
  final bool isLoading;

  const TranslationToggleButton({
    super.key,
    required this.showTranslation,
    required this.onToggle,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isLoading ? null : onToggle,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.appColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.appColor.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isLoading)
              const SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 1.5,
                  color: AppColors.appColor,
                ),
              )
            else
              const Icon(Icons.translate, size: 12, color: AppColors.appColor),
            const SizedBox(width: 4),
            Text(
              isLoading
                  ? 'Translating...'
                  : showTranslation
                  ? 'Show Original'
                  : 'Translate',
              style: const TextStyle(
                fontSize: 10,
                color: AppColors.appColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
