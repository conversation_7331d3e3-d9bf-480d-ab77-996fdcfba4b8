import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageType { text, image, video, audio, file, location, audioCall, videoCall }

enum MessageStatus { sending, sent, delivered, read, failed }

class ChatRoom {
  final String id;
  final List<String> participants; // List of user IDs
  final bool isGroupChat;
  final String? groupName;
  final String? groupImage;
  final String? lastMessage;
  final DateTime? lastMessageTime;
  final Map<String, int> unreadCount; // Map of user ID to unread count
  final DateTime createdAt;
  final String createdBy;

  ChatRoom({
    required this.id,
    required this.participants,
    this.isGroupChat = false,
    this.groupName,
    this.groupImage,
    this.lastMessage,
    this.lastMessageTime,
    required this.unreadCount,
    required this.createdAt,
    required this.createdBy,
  });

  factory ChatRoom.fromMap(Map<String, dynamic> map, String id) {
    return ChatRoom(
      id: id,
      participants: List<String>.from(map['participants'] ?? []),
      isGroupChat: map['isGroupChat'] ?? false,
      groupName: map['groupName'],
      groupImage: map['groupImage'],
      lastMessage: map['lastMessage'],
      lastMessageTime:
          map['lastMessageTime'] != null
              ? (map['lastMessageTime'] as Timestamp).toDate()
              : null,
      unreadCount: Map<String, int>.from(map['unreadCount'] ?? {}),
      createdAt:
          map['createdAt'] != null
              ? (map['createdAt'] as Timestamp).toDate()
              : DateTime.now(),
      createdBy: map['createdBy'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'participants': participants,
      'isGroupChat': isGroupChat,
      'groupName': groupName,
      'groupImage': groupImage,
      'lastMessage': lastMessage,
      'lastMessageTime':
          lastMessageTime != null ? Timestamp.fromDate(lastMessageTime!) : null,
      'unreadCount': unreadCount,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
    };
  }

  // Get the other participant in a one-to-one chat
  String getOtherParticipant(String currentUserId) {
    if (isGroupChat) return '';
    return participants.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ChatRoom) return false;

    return id == other.id &&
        _listEquals(participants, other.participants) &&
        isGroupChat == other.isGroupChat &&
        groupName == other.groupName &&
        groupImage == other.groupImage &&
        lastMessage == other.lastMessage &&
        lastMessageTime == other.lastMessageTime &&
        _mapEquals(unreadCount, other.unreadCount) &&
        createdAt == other.createdAt &&
        createdBy == other.createdBy;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      Object.hashAll(participants),
      isGroupChat,
      groupName,
      groupImage,
      lastMessage,
      lastMessageTime,
      Object.hashAllUnordered(
        unreadCount.entries.map((e) => Object.hash(e.key, e.value)),
      ),
      createdAt,
      createdBy,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  bool _mapEquals<T, U>(Map<T, U>? a, Map<T, U>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final T key in a.keys) {
      if (!b.containsKey(key) || b[key] != a[key]) return false;
    }
    return true;
  }
}

class Message {
  final String id;
  final String chatRoomId;
  final String senderId;
  final String? text;
  final String? mediaUrl;
  final MessageType type;
  final MessageStatus status;
  final DateTime timestamp;
  final List<String> readBy; // List of user IDs who have read the message
  final Map<String, dynamic>? metadata; // For additional data like file info

  Message({
    required this.id,
    required this.chatRoomId,
    required this.senderId,
    this.text,
    this.mediaUrl,
    required this.type,
    required this.status,
    required this.timestamp,
    required this.readBy,
    this.metadata,
  });

  factory Message.fromMap(Map<String, dynamic> map, String id) {
    return Message(
      id: id,
      chatRoomId: map['chatRoomId'] ?? '',
      senderId: map['senderId'] ?? '',
      text: map['text'],
      mediaUrl: map['mediaUrl'],
      type: MessageType.values[map['type'] ?? 0],
      status: MessageStatus.values[map['status'] ?? 0],
      timestamp:
          map['timestamp'] != null
              ? (map['timestamp'] as Timestamp).toDate()
              : DateTime.now(),
      readBy: List<String>.from(map['readBy'] ?? []),
      metadata: map['metadata'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'chatRoomId': chatRoomId,
      'senderId': senderId,
      'text': text,
      'mediaUrl': mediaUrl,
      'type': type.index,
      'status': status.index,
      'timestamp': Timestamp.fromDate(timestamp),
      'readBy': readBy,
      'metadata': metadata,
    };
  }

  Message copyWith({MessageStatus? status, List<String>? readBy}) {
    return Message(
      id: id,
      chatRoomId: chatRoomId,
      senderId: senderId,
      text: text,
      mediaUrl: mediaUrl,
      type: type,
      status: status ?? this.status,
      timestamp: timestamp,
      readBy: readBy ?? this.readBy,
      metadata: metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! Message) return false;

    return id == other.id &&
        chatRoomId == other.chatRoomId &&
        senderId == other.senderId &&
        text == other.text &&
        mediaUrl == other.mediaUrl &&
        type == other.type &&
        status == other.status &&
        timestamp == other.timestamp &&
        _listEquals(readBy, other.readBy) &&
        _mapEquals(metadata, other.metadata);
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      chatRoomId,
      senderId,
      text,
      mediaUrl,
      type,
      status,
      timestamp,
      Object.hashAll(readBy),
      metadata != null
          ? Object.hashAllUnordered(
            metadata!.entries.map((e) => Object.hash(e.key, e.value)),
          )
          : null,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  bool _mapEquals<T, U>(Map<T, U>? a, Map<T, U>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final T key in a.keys) {
      if (!b.containsKey(key) || b[key] != a[key]) return false;
    }
    return true;
  }
}
